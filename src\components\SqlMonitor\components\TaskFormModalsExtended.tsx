import React, { useEffect } from 'react';
import { Modal, Form, Input, Button, Row, Col, Table, Space, Tag, Tooltip, App } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { OtherInfo } from '../types';
import { modalStyles } from '../styles';

interface OtherInfoModalProps {
  visible: boolean;
  editingData?: OtherInfo;
  onCancel: () => void;
  onSubmit: (data: OtherInfo) => void;
}

interface SelectModalProps {
  visible: boolean;
  type: 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo';
  data: any[];
  selectedData?: any[];
  onCancel: () => void;
  onSubmit: (selectedItems: any[]) => void;
  onSearch?: (searchText: string) => void;
  multiple?: boolean;
}

// 其他信息Modal
export const OtherInfoModal: React.FC<OtherInfoModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const otherInfoData: OtherInfo = {
      id: editingData?.id || Date.now(),
      ...values,
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(otherInfoData);
  };

  return (
    <Modal 
      title={editingData ? '编辑其他信息' : '新增其他信息'} 
      open={visible} 
      onCancel={onCancel} 
      footer={null} 
      width={600}
    >
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='信息名称'
              name='name'
              rules={[{ required: true, message: '请输入信息名称' }]}
            >
              <Input placeholder='请输入信息名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='业务系统名称'
              name='business'
              rules={[{ required: true, message: '请输入业务系统名称' }]}
            >
              <Input placeholder='请输入业务系统名称' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='业务系统英文名称'
              name='business_en'
              rules={[{ required: true, message: '请输入业务系统英文名称' }]}
            >
              <Input placeholder='请输入业务系统英文名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='主机名称'
              name='hostname'
              rules={[{ required: true, message: '请输入主机名称' }]}
            >
              <Input placeholder='请输入主机名称' />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label='告警来源'
              name='location'
              rules={[{ required: true, message: '请输入告警来源' }]}
            >
              <Input placeholder='请输入告警来源' />
            </Form.Item>
          </Col>
        </Row>
        <div className='flex justify-end gap-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 选择Modal
export const SelectModal: React.FC<SelectModalProps> = ({ 
  visible, 
  type, 
  data, 
  selectedData = [], 
  onCancel, 
  onSubmit, 
  onSearch,
  multiple = true 
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<any[]>([]);
  const [searchText, setSearchText] = React.useState('');

  // 根据类型获取标题
  const getTitle = () => {
    const titles = {
      alert: '选择告警配置',
      alertSend: '选择告警发送',
      dbConnection: '选择数据库连接',
      otherInfo: '选择其他信息',
    };
    return titles[type];
  };

  // 根据类型获取列配置
  const getColumns = () => {
    const baseColumns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        width: 80,
      },
      {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        ellipsis: true,
      },
    ];

    // 根据类型添加特定列
    switch (type) {
      case 'alert':
        return [
          ...baseColumns,
          {
            title: '告警级别',
            dataIndex: 'severity',
            key: 'severity',
            width: 100,
            render: (severity: string) => (
              <Tag color={severity === 'critical' ? 'red' : severity === 'high' ? 'orange' : 'blue'}>
                {severity}
              </Tag>
            ),
          },
          {
            title: '告警类型',
            dataIndex: 'type',
            key: 'type',
            width: 120,
          },
        ];
      case 'dbConnection':
        return [
          ...baseColumns,
          {
            title: '数据库类型',
            dataIndex: 'db_type',
            key: 'db_type',
            width: 100,
            render: (type: string) => (
              <Tag color={type === 'mysql' ? 'blue' : 'green'}>
                {type.toUpperCase()}
              </Tag>
            ),
          },
          {
            title: '主机地址',
            dataIndex: 'host',
            key: 'host',
            width: 150,
            ellipsis: true,
          },
        ];
      default:
        return baseColumns;
    }
  };

  // 行选择配置
  const rowSelection = {
    type: multiple ? 'checkbox' : 'radio',
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };

  // 处理搜索
  const handleSearch = () => {
    onSearch?.(searchText);
  };

  // 处理提交
  const handleSubmit = () => {
    onSubmit(selectedRows);
  };

  return (
    <Modal
      title={getTitle()}
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={
        <div className='flex justify-between'>
          <div>
            已选择 {selectedRowKeys.length} 项
          </div>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type='primary' onClick={handleSubmit}>
              确定
            </Button>
          </Space>
        </div>
      }
    >
      {/* 搜索区域 */}
      <div className='mb-4'>
        <Space>
          <Input
            placeholder='搜索名称'
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onPressEnter={handleSearch}
            style={{ width: 200 }}
          />
          <Button type='primary' icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          <Button icon={<ReloadOutlined />} onClick={() => setSearchText('')}>
            重置
          </Button>
        </Space>
      </div>

      {/* 表格 */}
      <Table
        rowSelection={rowSelection as any}
        columns={getColumns()}
        dataSource={data}
        rowKey='id'
        size='small'
        scroll={{ y: 400 }}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showQuickJumper: true,
        }}
      />
    </Modal>
  );
};

// 导出组合对象
export const TaskFormModalsExtended = {
  OtherInfoModal,
  SelectModal,
};

export default TaskFormModalsExtended;
