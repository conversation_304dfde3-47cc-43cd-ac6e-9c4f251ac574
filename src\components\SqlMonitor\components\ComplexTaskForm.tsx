import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Button, Card, Row, Col, message, Tabs, InputNumber, TimePicker, Space, Table, Switch } from 'antd';
import { SettingOutlined, AlertOutlined, DatabaseOutlined, NotificationOutlined, InfoCircleOutlined, PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { TabsProps } from 'antd';

// 导入重构后的模块
import type { TaskBasic, TaskAlert, DBConnection, AlertSend, OtherInfo, TaskBasicFormDataAdd, TaskBasicFormDataUpdateOrDelete } from '../types';
import {
  WEEKDAY_OPTIONS,
  FREQUENCY_UNIT_OPTIONS,
  RETRY_FREQUENCY_UNIT_OPTIONS,
  DEFAULT_RETRY_NUM,
  DEFAULT_FREQUENCY,
  DEFAULT_RETRY_FREQUENCY,
  FORM_PLACEHOLDERS,
  FORM_BUTTON_TEXT,
  TASK_STATUS_OPTIONS,
  DB_TYPE_OPTIONS,
  ALERT_TYPE_OPTIONS,
  ALERT_SEVERITY_OPTIONS,
  SEND_TYPE_OPTIONS,
  ORACLE_CONNECT_METHOD_OPTIONS,
} from '../constants';
import { TaskService } from '../services';
import { formStyles } from '../styles';
import { formatFrequencyToString, parseFrequencyFromString } from '../../../utils/frequencyConverter';
import { TaskGroupSelect } from './common/TaskGroupSelect';
import { TaskFormModals } from './TaskFormModals';
import { TaskFormModalsExtended } from './TaskFormModalsExtended';

const { Option } = Select;

interface ComplexTaskFormProps {
  initialData?: TaskBasic;
  onSubmit?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
}

/**
 * 复合任务表单组件
 * 支持多标签页展示不同类型的配置
 */
const ComplexTaskForm: React.FC<ComplexTaskFormProps> = ({ initialData, onSubmit, onCancel, onReset }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [submitLoading, setSubmitLoading] = useState(false);

  // 是否为编辑模式
  const [isEditMode, setEditMode] = useState(false);

  // 各种数据状态
  const [alerts, setAlerts] = useState<TaskAlert[]>([]);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 可选择的数据
  const [availableAlerts, setAvailableAlerts] = useState<TaskAlert[]>([]);
  const [availableDbConnections, setAvailableDbConnections] = useState<DBConnection[]>([]);
  const [availableAlertSends, setAvailableAlertSends] = useState<AlertSend[]>([]);
  const [availableOtherInfos, setAvailableOtherInfos] = useState<OtherInfo[]>([]);

  // Modal状态
  const [alertModal, setAlertModal] = useState({
    visible: false,
    editingIndex: -1,
  });

  const [dbConnectionModal, setDbConnectionModal] = useState({
    visible: false,
  });

  const [alertSendModal, setAlertSendModal] = useState({
    visible: false,
    editingIndex: -1,
  });

  const [otherInfoModal, setOtherInfoModal] = useState({
    visible: false,
  });

  const [selectModal, setSelectModal] = useState({
    visible: false,
    type: '' as 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo',
    multiple: false,
  });

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      setEditMode(true);

      // 处理初始数据，转换为表单格式
      const formData = {
        name: initialData.name,
        group_name: initialData.group_name,
        status: initialData.status,
        weekday: initialData.weekday || [],
        frequency_value: initialData.frequency?.value || DEFAULT_FREQUENCY.value,
        frequency_unit: initialData.frequency?.unit || DEFAULT_FREQUENCY.unit,
        retry_frequency_value: initialData.retry_frequency?.value || DEFAULT_RETRY_FREQUENCY.value,
        retry_frequency_unit: initialData.retry_frequency?.unit || DEFAULT_RETRY_FREQUENCY.unit,
        retry_num: initialData.retry_num,
        start_time: initialData.start_time ? dayjs(initialData.start_time, 'HH:mm:ss') : undefined,
        end_time: initialData.end_time ? dayjs(initialData.end_time, 'HH:mm:ss') : undefined,
      };
      form.setFieldsValue(formData);

      // 加载关联数据
      loadRelatedData();
    } else {
      setEditMode(false);
      // 设置默认值
      form.setFieldsValue({
        status: 'enabled',
        frequency_value: DEFAULT_FREQUENCY.value,
        frequency_unit: DEFAULT_FREQUENCY.unit,
        retry_frequency_value: DEFAULT_RETRY_FREQUENCY.value,
        retry_frequency_unit: DEFAULT_RETRY_FREQUENCY.unit,
        retry_num: DEFAULT_RETRY_NUM,
      });
    }
  }, [initialData, form]);

  // 加载可选数据
  useEffect(() => {
    loadAvailableData();
  }, []);

  // 加载可选数据
  const loadAvailableData = async () => {
    try {
      const [alertsRes, dbConnectionsRes, alertSendsRes, otherInfosRes] = await Promise.all([
        TaskService.getAlerts(),
        TaskService.getDbConnections(),
        TaskService.getAlertSends(),
        TaskService.getOtherInfos(),
      ]);

      setAvailableAlerts(alertsRes);
      setAvailableDbConnections(dbConnectionsRes);
      setAvailableAlertSends(alertSendsRes);
      setAvailableOtherInfos(otherInfosRes);
    } catch (error) {
      console.error('加载可选数据失败:', error);
      message.error('加载数据失败');
    }
  };

  // 加载关联数据
  const loadRelatedData = async () => {
    if (!initialData) return;

    try {
      // 加载告警数据
      if (initialData.alert_task_id && initialData.alert_task_id.length > 0) {
        const alertData = await TaskService.getAlertsByIds(initialData.alert_task_id);
        setAlerts(alertData);
      }

      // 加载告警发送数据
      if (initialData.alert_send_id && initialData.alert_send_id.length > 0) {
        const alertSendData = await TaskService.getAlertSendsByIds(initialData.alert_send_id);
        setAlertSends(alertSendData);
      }

      // 加载数据库连接数据
      if (initialData.db_connection_id) {
        const dbData = await TaskService.getDbConnectionById(initialData.db_connection_id);
        setDbConnection(dbData);
      }

      // 加载其他信息数据
      if (initialData.other_info_id) {
        const otherData = await TaskService.getOtherInfoById(initialData.other_info_id);
        setOtherInfo(otherData);
      }
    } catch (error) {
      console.error('加载关联数据失败:', error);
      message.error('加载关联数据失败');
    }
  };

  // 表单验证
  const validateForm = async (): Promise<boolean> => {
    try {
      await form.validateFields();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  // 获取表单数据
  const getFormData = () => {
    return form.getFieldsValue();
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setAlerts([]);
    setAlertSends([]);
    setDbConnection(null);
    setOtherInfo(null);
  };

  // 表单提交处理
  const handleFormSubmit = async () => {
    const isValid = await validateForm();
    if (!isValid) {
      message.error('请检查表单数据');
      return;
    }

    setSubmitLoading(true);
    try {
      const formData = getFormData();

      // 转换表单数据为提交格式
      const submitData = {
        ...formData,
        weekday: Array.isArray(formData.weekday) ? formData.weekday.join(',') : formData.weekday,
        frequency: formatFrequencyToString(formData.frequency_value, formData.frequency_unit),
        retry_frequency: formatFrequencyToString(formData.retry_frequency_value, formData.retry_frequency_unit),
        start_time: formData.start_time ? formData.start_time.format('HH:mm:ss') : '',
        end_time: formData.end_time ? formData.end_time.format('HH:mm:ss') : '',
        alert_task_id: alerts.map(alert => `alert_${alert.id}`).join(','),
        alert_send_id: alertSends.map(send => `send_${send.id}`).join(','),
        db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
        other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
      };

      // 调用API
      if (isEditMode && initialData) {
        await TaskService.updateComplexForm(initialData.id, { id: initialData.id, ...submitData });
        message.success('更新任务成功');
      } else {
        await TaskService.saveComplexForm(submitData);
        message.success('创建任务成功');
      }

      onSubmit?.();
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error(isEditMode ? '更新任务失败' : '创建任务失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'basic',
      label: (
        <span>
          <SettingOutlined />
          <span style={{ marginLeft: 8 }}>基本信息</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card title='基本配置' size='small' className='mb-4'>
            <Row gutter={16} className='mb-4'>
              <Col span={8}>
                <Form.Item label='任务名称' name='name' rules={[{ required: true, message: '请输入任务名称' }]}>
                  <Input placeholder={FORM_PLACEHOLDERS.name} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='任务分组' name='group_name' rules={[{ required: true, message: '请选择任务分组' }]}>
                  <TaskGroupSelect placeholder={FORM_PLACEHOLDERS.group} allowClear dynamicSearch={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='任务状态' name='status' rules={[{ required: true, message: '请选择任务状态' }]}>
                  <Select placeholder='请选择任务状态'>
                    {TASK_STATUS_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className='mb-4'>
              <Col span={8}>
                <Form.Item label='开始时间' name='start_time' rules={[{ required: true, message: '请选择开始时间' }]}>
                  <TimePicker placeholder={FORM_PLACEHOLDERS.startTime} format='HH:mm:ss' className='w-full' />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label='结束时间' name='end_time' rules={[{ required: true, message: '请选择结束时间' }]}>
                  <TimePicker placeholder={FORM_PLACEHOLDERS.endTime} format='HH:mm:ss' className='w-full' />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className='mb-4'>
              <Col span={12}>
                <Form.Item label='执行星期' name='weekday' rules={[{ required: true, message: '请选择执行星期' }]}>
                  <Select mode='multiple' placeholder={FORM_PLACEHOLDERS.weekday} allowClear>
                    {WEEKDAY_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='执行频率'>
                  <Space.Compact style={{ width: '100%' }}>
                    <Form.Item name='frequency_value' noStyle rules={[{ required: true, message: '请输入频率值' }]}>
                      <InputNumber placeholder='频率值' min={1} style={{ width: '80%' }} />
                    </Form.Item>
                    <Form.Item name='frequency_unit' noStyle rules={[{ required: true, message: '请选择频率单位' }]}>
                      <Select placeholder='单位' style={{ width: '20%' }}>
                        {FREQUENCY_UNIT_OPTIONS.map(option => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} className='mb-4'>
              <Col span={12}>
                <Form.Item label='重试次数' name='retry_num' initialValue={DEFAULT_RETRY_NUM}>
                  <InputNumber placeholder={FORM_PLACEHOLDERS.retryNum} min={0} style={{width: '100%' }} className='w-full' />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='重试间隔'>
                  <Space.Compact style={{ width: '100%' }}>
                    <Form.Item name='retry_frequency_value' noStyle initialValue={DEFAULT_RETRY_FREQUENCY.value}>
                      <InputNumber placeholder='间隔值' min={1} style={{ width: '80%' }} />
                    </Form.Item>
                    <Form.Item name='retry_frequency_unit' noStyle initialValue={DEFAULT_RETRY_FREQUENCY.unit}>
                      <Select placeholder='单位' style={{ width: '20%' }}>
                        {RETRY_FREQUENCY_UNIT_OPTIONS.map(option => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </div>
      ),
    },
    {
      key: 'alert',
      label: (
        <span>
          <AlertOutlined />
          <span style={{ marginLeft: 8 }}>告警配置</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card
            title='告警触发条件'
            size='small'
            className='mb-4'
            extra={
              <Space>
                <Button type='primary' size='small' icon={<PlusOutlined />} onClick={() => setAlertModal({ visible: true, editingIndex: -1 })}>
                  新增告警
                </Button>
                <Button size='small' icon={<EyeOutlined />} onClick={() => setSelectModal({ visible: true, type: 'alert', multiple: true })}>
                  选择告警
                </Button>
              </Space>
            }
          >
            <Table
              dataSource={alerts}
              rowKey='id'
              size='small'
              pagination={false}
              columns={[
                {
                  title: '告警名称',
                  dataIndex: 'name',
                  key: 'name',
                  width: 150,
                },
                {
                  title: '告警级别',
                  dataIndex: 'severity',
                  key: 'severity',
                  width: 100,
                  render: (severity: string) => (
                    <span
                      style={{
                        color: severity === 'critical' ? '#ff4d4f' : severity === 'high' ? '#fa8c16' : severity === 'medium' ? '#faad14' : '#52c41a',
                      }}
                    >
                      {severity}
                    </span>
                  ),
                },
                {
                  title: '告警类型',
                  dataIndex: 'type',
                  key: 'type',
                  width: 100,
                },
                {
                  title: 'SQL语句',
                  dataIndex: 'sql',
                  key: 'sql',
                  ellipsis: true,
                  render: (sql: string) => <code style={{ fontSize: '12px', background: '#f5f5f5', padding: '2px 4px' }}>{sql}</code>,
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 120,
                  render: (_, _record, index) => (
                    <Space size='small'>
                      <Button type='text' size='small' icon={<EditOutlined />} onClick={() => setAlertModal({ visible: true, editingIndex: index })}>
                        编辑
                      </Button>
                      <Button
                        type='text'
                        size='small'
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          const newAlerts = alerts.filter((_, i) => i !== index);
                          setAlerts(newAlerts);
                        }}
                      >
                        删除
                      </Button>
                    </Space>
                  ),
                },
              ]}
            />
            {alerts.length === 0 && <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无告警配置，请点击"新增告警"或"选择告警"添加</div>}
          </Card>
        </div>
      ),
    },
    {
      key: 'database',
      label: (
        <span>
          <DatabaseOutlined />
          <span style={{ marginLeft: 8 }}>数据库连接</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card
            title='数据库连接配置'
            size='small'
            className='mb-4'
            extra={
              <Space>
                <Button type='primary' size='small' icon={<PlusOutlined />} onClick={() => setDbConnectionModal({ visible: true })}>
                  新增连接
                </Button>
                <Button size='small' icon={<EyeOutlined />} onClick={() => setSelectModal({ visible: true, type: 'dbConnection', multiple: false })}>
                  选择连接
                </Button>
              </Space>
            }
          >
            {dbConnection ? (
              <div style={{ padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <div>
                      <strong>连接名称:</strong> {dbConnection.name}
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <strong>数据库类型:</strong> {dbConnection.db_type.toUpperCase()}
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <strong>主机地址:</strong> {dbConnection.host}:{dbConnection.port}
                    </div>
                  </Col>
                </Row>
                <Row gutter={16} style={{ marginTop: '8px' }}>
                  <Col span={8}>
                    <div>
                      <strong>用户名:</strong> {dbConnection.user}
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <strong>数据库:</strong> {dbConnection.database || dbConnection.instance}
                    </div>
                  </Col>
                  <Col span={8}>
                    <Space>
                      <Button type='text' size='small' icon={<EditOutlined />} onClick={() => setDbConnectionModal({ visible: true })}>
                        编辑
                      </Button>
                      <Button type='text' size='small' danger icon={<DeleteOutlined />} onClick={() => setDbConnection(null)}>
                        删除
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无数据库连接配置，请点击"新增连接"或"选择连接"添加</div>
            )}
          </Card>
        </div>
      ),
    },
    {
      key: 'notification',
      label: (
        <span>
          <NotificationOutlined />
          <span style={{ marginLeft: 8 }}>告警发送</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card
            title='告警发送配置'
            size='small'
            className='mb-4'
            extra={
              <Space>
                <Button type='primary' size='small' icon={<PlusOutlined />} onClick={() => setAlertSendModal({ visible: true, editingIndex: -1 })}>
                  新增发送
                </Button>
                <Button size='small' icon={<EyeOutlined />} onClick={() => setSelectModal({ visible: true, type: 'alertSend', multiple: true })}>
                  选择发送
                </Button>
              </Space>
            }
          >
            <Table
              dataSource={alertSends}
              rowKey='id'
              size='small'
              pagination={false}
              columns={[
                {
                  title: '发送名称',
                  dataIndex: 'name',
                  key: 'name',
                  width: 150,
                },
                {
                  title: '发送类型',
                  dataIndex: 'type',
                  key: 'type',
                  width: 100,
                  render: (type: string) => (
                    <span
                      style={{
                        color: type === 'kafka' ? '#1890ff' : '#52c41a',
                        fontWeight: 'bold',
                      }}
                    >
                      {type.toUpperCase()}
                    </span>
                  ),
                },
                {
                  title: '配置信息',
                  key: 'config',
                  ellipsis: true,
                  render: (_, record) => {
                    const config = record.type === 'kafka' ? record.kafka_receiver : record.prometheus_receiver;
                    return <span style={{ fontSize: '12px' }}>{config.address}</span>;
                  },
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 120,
                  render: (_, _record, index) => (
                    <Space size='small'>
                      <Button type='text' size='small' icon={<EditOutlined />} onClick={() => setAlertSendModal({ visible: true, editingIndex: index })}>
                        编辑
                      </Button>
                      <Button
                        type='text'
                        size='small'
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          const newAlertSends = alertSends.filter((_, i) => i !== index);
                          setAlertSends(newAlertSends);
                        }}
                      >
                        删除
                      </Button>
                    </Space>
                  ),
                },
              ]}
            />
            {alertSends.length === 0 && <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无告警发送配置，请点击"新增发送"或"选择发送"添加</div>}
          </Card>
        </div>
      ),
    },
    {
      key: 'other',
      label: (
        <span>
          <InfoCircleOutlined />
          <span style={{ marginLeft: 8 }}>其他信息</span>
        </span>
      ),
      children: (
        <div className={formStyles.tabContent}>
          <Card
            title='附加信息配置'
            size='small'
            className='mb-4'
            extra={
              <Space>
                <Button type='primary' size='small' icon={<PlusOutlined />} onClick={() => setOtherInfoModal({ visible: true })}>
                  新增信息
                </Button>
                <Button size='small' icon={<EyeOutlined />} onClick={() => setSelectModal({ visible: true, type: 'otherInfo', multiple: false })}>
                  选择信息
                </Button>
              </Space>
            }
          >
            {otherInfo ? (
              <div style={{ padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <div>
                      <strong>信息名称:</strong> {otherInfo.name}
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <strong>业务系统:</strong> {otherInfo.business}
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <strong>英文名称:</strong> {otherInfo.business_en}
                    </div>
                  </Col>
                </Row>
                <Row gutter={16} style={{ marginTop: '8px' }}>
                  <Col span={8}>
                    <div>
                      <strong>主机名称:</strong> {otherInfo.hostname}
                    </div>
                  </Col>
                  <Col span={8}>
                    <div>
                      <strong>告警来源:</strong> {otherInfo.location}
                    </div>
                  </Col>
                  <Col span={8}>
                    <Space>
                      <Button type='text' size='small' icon={<EditOutlined />} onClick={() => setOtherInfoModal({ visible: true })}>
                        编辑
                      </Button>
                      <Button type='text' size='small' danger icon={<DeleteOutlined />} onClick={() => setOtherInfo(null)}>
                        删除
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无附加信息配置，请点击"新增信息"或"选择信息"添加</div>
            )}
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className='h-full flex flex-col'>
      <Form form={form} layout='vertical' className='flex-1 overflow-hidden'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          className={`${formStyles.formTabs} h-full`}
          style={{ height: '100%' }}
          tabBarStyle={{
            marginBottom: 0,
            paddingLeft: 16,
            paddingRight: 16,
            borderBottom: '1px solid #f0f0f0',
            background: '#fafafa',
          }}
        />
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className='flex justify-between items-center'>
          <div className={formStyles.footerHint}>{isEditMode ? '编辑任务信息' : '创建新任务'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            <Button
              onClick={() => {
                handleReset();
                onReset?.();
              }}
              className={`${formStyles.actionButton} ${formStyles.resetButton}`}
            >
              {FORM_BUTTON_TEXT.reset}
            </Button>
            <Button type='primary' loading={submitLoading} onClick={handleFormSubmit} className={`${formStyles.actionButton} ${isEditMode ? formStyles.confirmButton : formStyles.submitButton}`}>
              {isEditMode ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>

      {/* 告警Modal */}
      <TaskFormModals.AlertModal
        visible={alertModal.visible}
        editingData={alertModal.editingIndex >= 0 ? alerts[alertModal.editingIndex] : undefined}
        onCancel={() => setAlertModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          if (alertModal.editingIndex >= 0) {
            // 编辑
            const newAlerts = [...alerts];
            newAlerts[alertModal.editingIndex] = data;
            setAlerts(newAlerts);
          } else {
            // 新增
            setAlerts([...alerts, data]);
          }
          setAlertModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 数据库连接Modal */}
      <TaskFormModals.DbConnectionModal
        visible={dbConnectionModal.visible}
        editingData={dbConnection || undefined}
        onCancel={() => setDbConnectionModal({ visible: false })}
        onSubmit={data => {
          setDbConnection(data);
          setDbConnectionModal({ visible: false });
        }}
      />

      {/* 告警发送Modal */}
      <TaskFormModals.AlertSendModal
        visible={alertSendModal.visible}
        editingData={alertSendModal.editingIndex >= 0 ? alertSends[alertSendModal.editingIndex] : undefined}
        onCancel={() => setAlertSendModal({ visible: false, editingIndex: -1 })}
        onSubmit={data => {
          if (alertSendModal.editingIndex >= 0) {
            // 编辑
            const newAlertSends = [...alertSends];
            newAlertSends[alertSendModal.editingIndex] = data;
            setAlertSends(newAlertSends);
          } else {
            // 新增
            setAlertSends([...alertSends, data]);
          }
          setAlertSendModal({ visible: false, editingIndex: -1 });
        }}
      />

      {/* 其他信息Modal */}
      <TaskFormModalsExtended.OtherInfoModal
        visible={otherInfoModal.visible}
        editingData={otherInfo || undefined}
        onCancel={() => setOtherInfoModal({ visible: false })}
        onSubmit={data => {
          setOtherInfo(data);
          setOtherInfoModal({ visible: false });
        }}
      />

      {/* 选择Modal */}
      <TaskFormModalsExtended.SelectModal
        visible={selectModal.visible}
        type={selectModal.type}
        data={
          selectModal.type === 'alert' ? availableAlerts : selectModal.type === 'alertSend' ? availableAlertSends : selectModal.type === 'dbConnection' ? availableDbConnections : availableOtherInfos
        }
        multiple={selectModal.multiple}
        onCancel={() => setSelectModal({ visible: false, type: 'alert', multiple: false })}
        onSubmit={selectedItems => {
          if (selectModal.type === 'alert') {
            setAlerts([...alerts, ...selectedItems]);
          } else if (selectModal.type === 'alertSend') {
            setAlertSends([...alertSends, ...selectedItems]);
          } else if (selectModal.type === 'dbConnection' && selectedItems.length > 0) {
            setDbConnection(selectedItems[0]);
          } else if (selectModal.type === 'otherInfo' && selectedItems.length > 0) {
            setOtherInfo(selectedItems[0]);
          }
          setSelectModal({ visible: false, type: 'alert', multiple: false });
        }}
      />
    </div>
  );
};

export default ComplexTaskForm;
