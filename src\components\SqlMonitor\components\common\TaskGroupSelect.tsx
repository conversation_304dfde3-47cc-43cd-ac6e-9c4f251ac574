import React, { useState, useEffect, useCallback } from 'react';
import { Select, message } from 'antd';
import type { TaskBasicGroupFormData } from '../../types';
import { TaskService } from '../../services';

// 任务分组选择组件属性接口
interface TaskGroupSelectProps {
  /** 当前选中的值 */
  value?: string;
  /** 值变化回调 */
  onChange?: (value: string) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否允许清除 */
  allowClear?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示搜索功能 */
  showSearch?: boolean;
  /** 选择框大小 */
  size?: 'small' | 'middle' | 'large';
  /** 是否启用动态搜索模式（点击时加载数据） */
  dynamicSearch?: boolean;
}

/**
 * 任务分组选择组件
 *
 * 功能特性：
 * - 支持初始化加载和动态搜索两种模式
 * - 支持搜索过滤
 * - 支持清除选择
 * - 加载状态显示
 * - 错误处理
 *
 * @example
 * ```tsx
 * // 基础用法（初始化加载）
 * <TaskGroupSelect
 *   value={selectedGroup}
 *   onChange={setSelectedGroup}
 *   placeholder="请选择任务分组"
 * />
 *
 * // 动态搜索模式（点击时加载）
 * <TaskGroupSelect
 *   value={selectedGroup}
 *   onChange={setSelectedGroup}
 *   placeholder="请选择任务分组"
 *   dynamicSearch={true}
 * />
 * ```
 */
export const TaskGroupSelect: React.FC<TaskGroupSelectProps> = ({
  value,
  onChange,
  placeholder = '请选择任务分组',
  allowClear = false,
  className,
  style,
  disabled = false,
  showSearch = true,
  size = 'middle',
  dynamicSearch = false,
}) => {
  // 状态管理
  const [options, setOptions] = useState<TaskBasicGroupFormData[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);

  // 加载分组数据
  const loadGroupData = useCallback(async () => {
    if (loading) return;

    setLoading(true);
    try {
      const response = await TaskService.getTaskGroupAll();
      if (response && response.data) {
        setOptions(response.data);
        setHasLoaded(true);
      }
    } catch (error) {
      console.error('加载任务分组失败:', error);
      message.error('加载任务分组失败');
    } finally {
      setLoading(false);
    }
  }, [loading]);

  // 初始化加载数据（非动态搜索模式）
  useEffect(() => {
    if (!dynamicSearch && !hasLoaded) {
      loadGroupData();
    }
  }, [dynamicSearch, hasLoaded, loadGroupData]);

  // 下拉框展开时加载数据（动态搜索模式）
  const handleDropdownVisibleChange = useCallback(
    (open: boolean) => {
      if (dynamicSearch && open && !hasLoaded) {
        loadGroupData();
      }
    },
    [dynamicSearch, hasLoaded, loadGroupData]
  );

  // 搜索过滤函数
  const filterOption = useCallback((input: string, option: any) => {
    const label = option?.label || '';
    return label.toLowerCase().includes(input.toLowerCase());
  }, []);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      className={className}
      style={style}
      disabled={disabled}
      showSearch={showSearch}
      size={size}
      loading={loading}
      onDropdownVisibleChange={handleDropdownVisibleChange}
      filterOption={filterOption}
      notFoundContent={loading ? '加载中...' : '暂无数据'}
      options={options.map(group => ({
        label: group.name,
        value: group.name,
        key: group.id,
      }))}
    />
  );
};

export default TaskGroupSelect;
