/**
 * 表格相关类型定义
 */

import React from 'react';
import { TaskBasic } from './task';

/**
 * 表格选择状态类型
 */
export type TaskSelectionState = {
  selectedRowKeys: React.Key[];
  selectedRows: TaskBasic[];
};

/**
 * 表格排序状态类型
 */
export type TableSortState = {
  field?: string;
  order?: 'ascend' | 'descend' | null;
};

/**
 * 表格筛选状态类型
 */
export type TableFilterState = Record<string, any>;

/**
 * 表格分页状态类型
 */
export type TablePaginationState = {
  current: number;
  page_size: number;
};

/**
 * 表格配置类型
 */
export interface TableConfig {
  size?: 'small' | 'middle' | 'large';
  bordered?: boolean;
  showHeader?: boolean;
  scroll?: {
    x?: number;
    y?: number;
  };
}

/**
 * 表格列配置类型
 */
export interface TableColumnConfig {
  width?: number;
  fixed?: 'left' | 'right';
  ellipsis?: boolean;
  sorter?: boolean;
  filterable?: boolean;
}

/**
 * 表格操作类型
 */
export type TableAction = 'edit' | 'delete' | 'view' | 'copy';

/**
 * 表格批量操作类型
 */
export type BatchAction = 'delete' | 'export' | 'enable' | 'disable';
